import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree_adapter.dart';

/// 部门树状选择器组件
/// 重构后使用通用的 AppTree 组件，保留API调用逻辑
class DepartmentTree extends StatefulWidget {
  /// 是否显示复选框
  final bool showCheckbox;

  /// 在显示复选框的情况下，控制父子节点选中状态是否严格独立，不互相关联
  /// 当为 false（默认值）时：父子节点选中状态相互关联（选中父节点会自动选中所有子节点，选中所有子节点会自动选中父节点）
  /// 当为 true 时：父子节点选中状态完全独立，互不影响
  final bool checkStrictly;

  /// 节点点击回调
  final void Function(DepartmentModel department)? onNodeTap;

  /// 节点选择回调（复选框点击时触发）
  final void Function(DepartmentModel department, bool isSelected)? onNodeSelected;

  /// 搜索查询字符串
  final String? searchQuery;

  /// 数据加载完成回调
  final VoidCallback? onDataLoaded;

  /// 是否默认高亮选中第一个最上级部门（parentId为null的部门）
  /// 这会设置节点的点击高亮状态，而不是复选框状态
  final bool defaultSelectTopLevel;

  const DepartmentTree({
    super.key,
    this.showCheckbox = false,
    this.checkStrictly = false,
    this.onNodeTap,
    this.onNodeSelected,
    this.searchQuery,
    this.onDataLoaded,
    this.defaultSelectTopLevel = true,
  });

  @override
  State<DepartmentTree> createState() => DepartmentTreeState();
}

// 将 State 类改为公开，以便外部可以访问
class DepartmentTreeState extends State<DepartmentTree> {
  final Map<String, dynamic> _reqParams = {'PageIndex': 1, 'PageSize': 20000};

  PagesModel<DepartmentModel> _pages = PagesModel();
  List<DepartmentModel> _list = [];
  List<AppTreeNode<DepartmentTreeAdapter>> _treeNodes = [];

  // 加载状态管理
  bool _isLoading = false;

  // 搜索状态管理
  String? _searchQuery;

  @override
  void initState() {
    super.initState();
    _loadDepartmentData();
  }

  @override
  void didUpdateWidget(DepartmentTree oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果搜索查询发生变化，不需要重新加载数据，AppTree会自动处理
    // 如果默认选中最上级部门的设置发生变化，需要更新高亮状态
    if (oldWidget.defaultSelectTopLevel != widget.defaultSelectTopLevel) {
      if (widget.defaultSelectTopLevel) {
        // 启用默认选中，高亮选中第一个最上级部门
        setState(() {
          _selectTopLevelDepartments();
        });
      } else if (!widget.defaultSelectTopLevel) {
        // 禁用默认选中，清除所有高亮状态
        setState(() {
          _clearAllNodesSelection(_treeNodes);
        });
      }
    }
  }

  /// 加载部门数据
  Future<void> _loadDepartmentData() async {
    if (_isLoading) return; // 防止重复加载

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await DepartmentApi.getList(_reqParams);
      if (mounted) {
        setState(() {
          _pages = PagesModel.fromJson(
            response,
            (json) => DepartmentModel.fromJson(json as Map<String, dynamic>),
          );
          _list = _pages.items;
          _buildTreeStructure();
          _isLoading = false;
        });
        // 数据加载完成后调用回调
        widget.onDataLoaded?.call();
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 构建树形结构
  void _buildTreeStructure() {
    // 使用适配器构建树结构
    _treeNodes = DepartmentTreeBuilder.buildTree(_list);

    // 如果启用了默认选中最上级部门，则自动高亮选中第一个根节点
    if (widget.defaultSelectTopLevel) {
      _selectTopLevelDepartments();
    }
  }

  /// 高亮选中第一个最上级部门（parentId为null的部门）
  void _selectTopLevelDepartments() {
    if (_treeNodes.isNotEmpty) {
      // 先清除所有节点的选中状态
      _clearAllNodesSelection(_treeNodes);

      // 只高亮选中第一个根节点（第一个最上级部门）
      final firstNode = _treeNodes.first;
      firstNode.isSelected = true;

      // 触发点击回调
      widget.onNodeTap?.call(firstNode.data.department);
    }
  }

  /// 递归清除所有节点的选中状态
  void _clearAllNodesSelection(List<AppTreeNode<DepartmentTreeAdapter>> nodes) {
    for (final node in nodes) {
      node.isSelected = false;
      _clearAllNodesSelection(node.children);
    }
  }

  /// 刷新数据
  void refresh() {
    _loadDepartmentData();
  }

  /// 获取所有选中的部门数据
  List<DepartmentModel> getAllCheckedDepartments() {
    return DepartmentTreeBuilder.extractCheckedDepartments(_treeNodes);
  }

  /// 重置所有复选框状态
  void resetAllNodesCheck() {
    setState(() {
      _resetNodesCheckState(_treeNodes);
    });
  }

  /// 递归重置节点复选框状态
  void _resetNodesCheckState(List<AppTreeNode<DepartmentTreeAdapter>> nodes) {
    for (final node in nodes) {
      node.isChecked = false;
      node.isIndeterminate = false;
      // 避免影响树结构的层级缓存
      _resetNodesCheckState(node.children);
    }
  }

  /// 选中节点
  void checkNode(String nodeId) {
    final node = _findNodeById(nodeId);
    if (node != null) {
      setState(() {
        node.isChecked = true;
        node.isIndeterminate = false;
      });
    }
  }

  /// 取消选中节点
  void uncheckNode(String nodeId) {
    final node = _findNodeById(nodeId);
    if (node != null) {
      setState(() {
        node.isChecked = false;
        node.isIndeterminate = false;
      });
    }
  }

  /// 设置搜索查询
  void setSearchQuery(String query) {
    setState(() {
      _searchQuery = query.trim().isEmpty ? null : query.trim();
    });
  }

  /// 手动高亮选中或取消选中第一个最上级部门
  void setFirstTopLevelDepartmentSelected(bool selected) {
    setState(() {
      if (_treeNodes.isNotEmpty) {
        if (selected) {
          // 先清除所有节点的选中状态
          _clearAllNodesSelection(_treeNodes);
          // 高亮选中第一个根节点
          final firstNode = _treeNodes.first;
          firstNode.isSelected = true;
          // 触发点击回调
          widget.onNodeTap?.call(firstNode.data.department);
        } else {
          // 清除所有高亮状态
          _clearAllNodesSelection(_treeNodes);
        }
      }
    });
  }

  /// 根据ID查找节点
  AppTreeNode<DepartmentTreeAdapter>? _findNodeById(String nodeId) {
    for (final node in _treeNodes) {
      final found = node.findNodeById(nodeId);
      if (found != null) return found;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return AppTree<DepartmentTreeAdapter>(
      nodes: _treeNodes,
      showCheckbox: widget.showCheckbox,
      checkStrictly: widget.checkStrictly,
      searchQuery: _searchQuery,
      isLoading: _isLoading,
      onRetry: _loadDepartmentData,
      style: AppTreeStyle.defaultStyle(), // 添加默认样式配置
      onNodeTap: (adapter) {
        widget.onNodeTap?.call(adapter.department);
      },
      onNodeSelected: (adapter, isSelected) {
        widget.onNodeSelected?.call(adapter.department, isSelected);
      },
    );
  }
}
